services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: highfive-app
    restart: unless-stopped
    ports:
      - "${APP_PORT:-80}:80"
      - "8000:80"
    volumes:
      - ../highfive-backend:/var/www/html
      - ~/.composer/auth.json:/root/.composer/auth.json:ro
      - ~/.ssh:/root/.ssh:ro
      # Configuration files mounted as volumes for easy editing without rebuilds
      - ./docker/supervisord.conf:/etc/supervisor/conf.d/supervisord.conf:ro
      - ./docker/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/php.ini:/usr/local/etc/php/conf.d/99-custom.ini:ro
    networks:
      - highfive-network
    depends_on:
      database:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/up"]
      interval: 30s
      timeout: 10s
      retries: 3

  database:
    image: postgres:17-alpine
    container_name: highfive-database
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${DB_DATABASE:-highfive}
      POSTGRES_USER: ${DB_USERNAME:-highfive}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-secret}
    ports:
      - "${DB_PORT:-5432}:5432"
    volumes:
      - database_data:/var/lib/postgresql/data
    networks:
      - highfive-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USERNAME:-highfive}"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: highfive-redis
    restart: unless-stopped
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    networks:
      - highfive-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  opensearch:
    build:
      context: docker/opensearch
      dockerfile: Dockerfile
    container_name: highfive-opensearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - plugins.security.disabled=true
      - "OPENSEARCH_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "${OPENSEARCH_PORT:-9200}:9200"
    volumes:
      - opensearch_data:/usr/share/opensearch/data
      # Configuration files mounted as volumes for easy editing without rebuilds
      - ./docker/opensearch/opensearch.yml:/usr/share/opensearch/config/opensearch.yml:ro
    networks:
      - highfive-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9200/_cluster/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  opensearch-dashboards:
    image: opensearchproject/opensearch-dashboards:3.1.0
    container_name: highfive-opensearch-dashboards
    restart: unless-stopped
    environment:
      - OPENSEARCH_HOSTS=http://opensearch:9200
      - DISABLE_SECURITY_DASHBOARDS_PLUGIN=true
      - i18n.locale=en
    ports:
      - "${OPENSEARCH_DASHBOARDS_PORT:-5601}:5601"
    networks:
      - highfive-network
    depends_on:
      opensearch:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5601/api/status"]
      interval: 30s
      timeout: 10s
      retries: 5

  mailpit:
    image: axllent/mailpit:latest
    container_name: highfive-mailpit
    restart: unless-stopped
    ports:
      - "${MAILPIT_HTTP_PORT:-8025}:8025"
    networks:
      - highfive-network

  frontend:
    image: node:20-alpine
    container_name: highfive-frontend
    working_dir: /app
    command: sh -c "rm -rf node_modules && npm install && npm run start"
    volumes:
      - ../clinic-portal-web-app:/app
    ports:
      - "3000:3000"
    networks:
      - highfive-network
    depends_on:
      - app

networks:
  highfive-network:
    driver: bridge

volumes:
  database_data:
  redis_data:
  opensearch_data: